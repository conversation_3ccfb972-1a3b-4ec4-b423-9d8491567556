"use client";

import { useState } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>eader } from "../ui/modal";
import { ProfileTab } from "./profile-tab";
import { SecurityTab } from "./security-tab";

type TabType = "profile" | "security";

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UserProfileModal = ({ isOpen, onClose }: UserProfileModalProps) => {
  const [activeTab, setActiveTab] = useState<TabType>("profile");

  const tabs = [
    { id: "profile" as const, label: "Profile" },
    { id: "security" as const, label: "Security" },
  ];

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <ModalHeader>
        <span className="font-plus-jakarta text-[32px] font-bold leading-[48px]">
          User Profile
        </span>
      </ModalHeader>
      <ModalBody>
        <div className="grid grid-cols-12 gap-6 min-h-[500px]">
          {/* Left Panel - Navigation */}
          <div className="col-span-3 border-r border-gray-200 pr-6">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    w-full text-left px-4 py-3 rounded-lg font-poppins text-sm font-medium transition-colors
                    ${
                      activeTab === tab.id
                        ? "bg-purple-50 text-purple-700 border border-purple-200"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }
                  `}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Right Panel - Content */}
          <div className="col-span-9 pl-2">
            {activeTab === "profile" && <ProfileTab />}
            {activeTab === "security" && <SecurityTab />}
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};