"use client";

import type { SessionResource } from "@clerk/types";

import { Button } from "../ui/button";

interface ActiveDevicesSectionProps {
  sessions: SessionResource[] | null | undefined;
  sessionsLoaded: boolean;
}

export const ActiveDevicesSection = ({
  sessions,
  sessionsLoaded,
}: ActiveDevicesSectionProps) => {
  const formatDeviceName = () => {
    // For now, we'll use a simple device detection
    // In a real implementation, you might want to parse user agent from session data
    return "Desktop";
  };

  const formatBrowserInfo = () => {
    // For now, we'll show a generic browser info
    // In a real implementation, you might want to parse user agent from session data
    return "Browser";
  };

  const formatLastActive = (timestamp: string | number | Date) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffInHours < 1) {
      return "Active now";
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) {
        return "Yesterday";
      } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
      } else {
        return date.toLocaleDateString();
      }
    }
  };

  const getCurrentSession = () => {
    if (!sessions) return null;
    return sessions.find((session) => session.status === "active");
  };

  const currentSession = getCurrentSession();

  return (
    <div>
      <h3 className="font-poppins mb-4 text-lg font-semibold text-gray-800">
        Active Devices
      </h3>

      {!sessionsLoaded ? (
        <div className="p-4 text-center text-gray-500">
          Loading active sessions...
        </div>
      ) : sessions && sessions.length > 0 ? (
        <div className="space-y-3">
          {sessions.map((session) => {
            const isCurrentSession = session.id === currentSession?.id;
            const deviceName = formatDeviceName();
            const browserInfo = formatBrowserInfo();
            const lastActive = formatLastActive(session.lastActiveAt);
            const ipAddress = "Unknown IP"; // Clerk doesn't expose IP in session data

            return (
              <div
                key={session.id}
                className="rounded-lg border border-gray-200 bg-gray-50 p-4"
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {deviceName}
                      </span>
                      {isCurrentSession && (
                        <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                          This device
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">{browserInfo}</div>
                    <div className="text-sm text-gray-500">{ipAddress}</div>
                    <div className="text-sm text-gray-500">{lastActive}</div>
                  </div>

                  {!isCurrentSession && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Sign out of specific session
                        session.end();
                      }}
                      className="text-red-600 hover:border-red-300 hover:text-red-700"
                    >
                      Sign out
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="p-4 text-center text-gray-500">
          No active sessions found
        </div>
      )}
    </div>
  );
};
