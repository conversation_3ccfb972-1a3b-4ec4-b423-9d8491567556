"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

import { useAuthentication } from "@/stores/authentication";

import { Button } from "../ui/button";
import { InputField } from "../ui/form/input";

const profileFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileFormSchema>;

export const ProfileTab = () => {
  const { user } = useAuthentication();
  const [isEditing, setIsEditing] = useState(false);

  const methods = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
    },
  });

  const { handleSubmit, reset } = methods;

  const handleEdit = () => {
    setIsEditing(true);
    reset({
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
    });
  };

  const handleCancel = () => {
    setIsEditing(false);
    reset({
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
    });
  };

  const onSubmit = (data: ProfileFormData) => {
    // TODO: Implement API call to update user profile
    console.log("Profile update data:", data);
    setIsEditing(false);
  };

  if (!user) {
    return (
      <div className="flex h-64 items-center justify-center">
        <span className="text-gray-500">Loading user information...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="font-plus-jakarta text-2xl font-bold text-gray-900">
          Profile
        </h2>
        {!isEditing && (
          <Button variant="outline" onClick={handleEdit} className="px-4 py-2">
            Edit
          </Button>
        )}
      </div>

      {/* Content */}
      {isEditing ? (
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  First Name
                </label>
                <InputField
                  name="firstName"
                  placeholder="Enter your first name"
                />
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Last Name
                </label>
                <InputField
                  name="lastName"
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <InputField
                name="email"
                type="email"
                placeholder="Enter your email"
                disabled
                className="cursor-not-allowed bg-gray-50"
              />
              <p className="mt-1 text-xs text-gray-500">
                Email address cannot be changed
              </p>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Phone Number
              </label>
              <InputField
                name="phone"
                type="tel"
                placeholder="Enter your phone number"
              />
            </div>

            <div className="flex justify-end space-x-3 border-t border-gray-200 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                Save
              </Button>
            </div>
          </form>
        </FormProvider>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                First Name
              </label>
              <div className="font-medium text-gray-900">
                {user.firstName || "Not provided"}
              </div>
            </div>
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Last Name
              </label>
              <div className="font-medium text-gray-900">
                {user.lastName || "Not provided"}
              </div>
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Email Address
            </label>
            <div className="font-medium text-gray-900">{user.email}</div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Phone Number
            </label>
            <div className="font-medium text-gray-900">
              {user.phone || "Not provided"}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
