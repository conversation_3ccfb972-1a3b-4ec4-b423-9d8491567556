"use client";

import { useSessionList, useUser } from "@clerk/nextjs";

import { ActiveDevicesSection } from "./active-devices-section";
import { PasswordSection } from "./password-section";

export const SecurityTab = () => {
  const { user } = useUser();
  const { sessions, isLoaded: sessionsLoaded } = useSessionList();

  return (
    <div className="space-y-8">
      <div>
        <h2 className="font-plus-jakarta mb-6 text-2xl font-bold text-gray-900">
          Security
        </h2>

        <div className="space-y-6">
          <PasswordSection user={user} />
          <ActiveDevicesSection
            sessions={sessions}
            sessionsLoaded={sessionsLoaded}
          />
        </div>
      </div>
    </div>
  );
};
