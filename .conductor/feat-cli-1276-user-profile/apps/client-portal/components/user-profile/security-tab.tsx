"use client";

import { use<PERSON><PERSON><PERSON>, useSessionList,useUser } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect,useState } from "react";
import { FormProvider,useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "../ui/button";
import { CheckboxField } from "../ui/form/checkbox";
import { InputField } from "../ui/form/input";

const passwordFormSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
  signOutAllDevices: z.boolean().default(false),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type PasswordFormData = z.infer<typeof passwordFormSchema>;

export const SecurityTab = () => {
  const { user } = useUser();
  const { signOut } = useClerk();
  const { sessions, isLoaded: sessionsLoaded } = useSessionList();
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const methods = useForm<PasswordFormData>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      signOutAllDevices: false,
    },
  });

  const { handleSubmit, reset } = methods;

  const handleUpdatePassword = () => {
    setIsUpdatingPassword(true);
    reset();
  };

  const handleCancelPasswordUpdate = () => {
    setIsUpdatingPassword(false);
    reset();
  };

  const onSubmit = async (data: PasswordFormData) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      // Update password using Clerk
      await user.updatePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        signOutOfOtherSessions: data.signOutAllDevices,
      });

      setIsUpdatingPassword(false);
      reset();
    } catch (error) {
      console.error("Password update failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDeviceName = (session: any) => {
    // For now, we'll use a simple device detection
    // In a real implementation, you might want to parse user agent from session data
    return "Desktop";
  };

  const formatBrowserInfo = (session: any) => {
    // For now, we'll show a generic browser info
    // In a real implementation, you might want to parse user agent from session data
    return "Browser";
  };

  const formatLastActive = (timestamp: string | number | Date) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return "Active now";
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) {
        return "Yesterday";
      } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
      } else {
        return date.toLocaleDateString();
      }
    }
  };

  const getCurrentSession = () => {
    if (!sessions) return null;
    return sessions.find(session => session.status === 'active');
  };

  const currentSession = getCurrentSession();

  return (
    <div className="space-y-8">
      {/* Password Section */}
      <div>
        <h2 className="font-plus-jakarta text-2xl font-bold text-gray-900 mb-6">
          Security
        </h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="font-poppins text-lg font-semibold text-gray-800 mb-4">
              Password
            </h3>
            
            {!isUpdatingPassword ? (
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="font-mono text-gray-600">••••••••••</span>
                </div>
                <Button
                  variant="outline"
                  onClick={handleUpdatePassword}
                  className="px-4 py-2"
                >
                  Update password
                </Button>
              </div>
            ) : (
              <FormProvider {...methods}>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Password
                    </label>
                    <InputField
                      name="currentPassword"
                      type="password"
                      placeholder="Enter your current password"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      New Password
                    </label>
                    <InputField
                      name="newPassword"
                      type="password"
                      placeholder="Enter your new password"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm Password
                    </label>
                    <InputField
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm your new password"
                    />
                  </div>

                  <div className="pt-2">
                    <div className="flex items-center space-x-2">
                      <CheckboxField
                        name="signOutAllDevices"
                        className="text-purple-600"
                      />
                      <label className="text-sm text-gray-700">
                        Sign out of all other devices
                      </label>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancelPasswordUpdate}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      isLoading={isSubmitting}
                    >
                      Save
                    </Button>
                  </div>
                </form>
              </FormProvider>
            )}
          </div>

          {/* Active Devices Section */}
          <div>
            <h3 className="font-poppins text-lg font-semibold text-gray-800 mb-4">
              Active Devices
            </h3>
            
            {!sessionsLoaded ? (
              <div className="p-4 text-center text-gray-500">
                Loading active sessions...
              </div>
            ) : sessions && sessions.length > 0 ? (
              <div className="space-y-3">
                {sessions.map((session) => {
                  const isCurrentSession = session.id === currentSession?.id;
                  const deviceName = formatDeviceName(session);
                  const browserInfo = formatBrowserInfo(session);
                  const lastActive = formatLastActive(session.lastActiveAt);
                  const ipAddress = "Unknown IP"; // Clerk doesn't expose IP in session data
                  
                  return (
                    <div
                      key={session.id}
                      className="p-4 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">
                              {deviceName}
                            </span>
                            {isCurrentSession && (
                              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                                This device
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            {browserInfo}
                          </div>
                          <div className="text-sm text-gray-500">
                            {ipAddress}
                          </div>
                          <div className="text-sm text-gray-500">
                            {lastActive}
                          </div>
                        </div>
                        
                        {!isCurrentSession && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Sign out of specific session
                              session.end();
                            }}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            Sign out
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                No active sessions found
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};