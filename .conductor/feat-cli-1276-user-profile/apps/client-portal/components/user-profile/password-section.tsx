"use client";

import type { UserResource } from "@clerk/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "../ui/button";
import { CheckboxField } from "../ui/form/checkbox";
import { InputField } from "../ui/form/input";

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    signOutAllDevices: z.boolean().default(false),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type PasswordFormData = z.infer<typeof passwordFormSchema>;

interface PasswordSectionProps {
  user: UserResource | null | undefined;
}

export const PasswordSection = ({ user }: PasswordSectionProps) => {
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const methods = useForm<PasswordFormData>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      signOutAllDevices: false,
    },
  });

  const { handleSubmit, reset } = methods;

  const handleUpdatePassword = () => {
    setIsUpdatingPassword(true);
    reset();
  };

  const handleCancelPasswordUpdate = () => {
    setIsUpdatingPassword(false);
    reset();
  };

  const onSubmit = async (data: PasswordFormData) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      // Update password using Clerk
      await user.updatePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        signOutOfOtherSessions: data.signOutAllDevices,
      });

      setIsUpdatingPassword(false);
      reset();
    } catch (error) {
      console.error("Password update failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h3 className="font-poppins mb-4 text-lg font-semibold text-gray-800">
        Password
      </h3>

      {!isUpdatingPassword ? (
        <div className="flex items-center justify-between rounded-lg bg-gray-50 p-4">
          <div className="flex items-center space-x-3">
            <span className="font-mono text-gray-600">••••••••••</span>
          </div>
          <Button
            variant="outline"
            onClick={handleUpdatePassword}
            className="px-4 py-2"
          >
            Update password
          </Button>
        </div>
      ) : (
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Current Password
              </label>
              <InputField
                name="currentPassword"
                type="password"
                placeholder="Enter your current password"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                New Password
              </label>
              <InputField
                name="newPassword"
                type="password"
                placeholder="Enter your new password"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <InputField
                name="confirmPassword"
                type="password"
                placeholder="Confirm your new password"
              />
            </div>

            <div className="pt-2">
              <div className="flex items-center space-x-2">
                <CheckboxField
                  name="signOutAllDevices"
                  className="text-purple-600"
                />
                <label className="text-sm text-gray-700">
                  Sign out of all other devices
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 border-t border-gray-200 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancelPasswordUpdate}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" variant="primary" isLoading={isSubmitting}>
                Save
              </Button>
            </div>
          </form>
        </FormProvider>
      )}
    </div>
  );
};
